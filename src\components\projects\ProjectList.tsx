'use client';

import { useState, useEffect, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { PermissionAwareTable } from '@/components/ui/permission-aware-table';
import { EmptyState } from '@/components/ui/empty-state';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { useEnhancedDeleteDialog, deleteConfigurations } from '@/components/ui/enhanced-delete-dialog';
import { PermissionGate } from '@/components/ui/permission-gate';
import { useToast } from '@/hooks/use-toast';
import { PermissionResource, PermissionAction } from '@/types';
import { 
  Eye, 
  Edit, 
  Trash2, 
  Search, 
  Filter,
  FolderOpen,
  Calendar,
  Users,
  DollarSign,
  Clock,
  TrendingUp,
  CheckCircle,
  XCircle,
  Pause,
  Plus
} from 'lucide-react';
import Link from 'next/link';

interface Project {
  id: string;
  name: string;
  description?: string;
  status: string;
  priority: string;
  progress: number;
  startDate?: string;
  endDate?: string;
  budget?: number;
  estimatedHours?: number;
  actualHours?: number;
  clientPortalEnabled: boolean;
  createdAt: string;
  updatedAt: string;
  manager?: {
    id: string;
    firstName?: string;
    lastName?: string;
    email: string;
  };
  opportunity?: {
    id: string;
    title: string;
    value?: number;
  };
  _count?: {
    tasks: number;
    milestones: number;
  };
}

interface ProjectListFilters {
  search: string;
  status: string;
  priority: string;
  managerId: string;
  page: number;
  limit: number;
}

export function ProjectList() {
  const router = useRouter();
  const { toast } = useToast();
  const { showDeleteDialog, DialogComponent } = useEnhancedDeleteDialog();
  
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalPages, setTotalPages] = useState(1);
  const [total, setTotal] = useState(0);
  const [filters, setFilters] = useState<ProjectListFilters>({
    search: '',
    status: '',
    priority: '',
    managerId: '',
    page: 1,
    limit: 10
  });

  // Load projects
  const loadProjects = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      
      Object.entries(filters).forEach(([key, value]) => {
        if (value) {
          params.append(key, value.toString());
        }
      });

      const response = await fetch(`/api/projects?${params.toString()}`);
      if (!response.ok) {
        throw new Error('Failed to fetch projects');
      }

      const data = await response.json();
      setProjects(data.data.projects);
      setTotalPages(data.data.totalPages);
      setTotal(data.data.total);
    } catch (error) {
      console.error('Error loading projects:', error);
      toast({
        title: 'Error',
        description: 'Failed to load projects',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Delete project
  const handleDelete = async (projectId: string) => {
    try {
      const response = await fetch(`/api/projects/${projectId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete project');
      }

      toast({
        title: 'Success',
        description: 'Project deleted successfully',
      });

      // Reload projects
      loadProjects();
    } catch (error) {
      console.error('Error deleting project:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to delete project',
        variant: 'destructive',
      });
    }
  };

  // Handle filter changes
  const handleFilterChange = (key: keyof ProjectListFilters, value: string | number) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: key !== 'page' ? 1 : value // Reset to page 1 when other filters change
    }));
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    handleFilterChange('page', page);
  };

  useEffect(() => {
    loadProjects();
  }, [filters]);

  // Status badge helper
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      planning: { label: 'Planning', color: 'bg-blue-100 text-blue-800', icon: Clock },
      in_progress: { label: 'In Progress', color: 'bg-green-100 text-green-800', icon: TrendingUp },
      on_hold: { label: 'On Hold', color: 'bg-yellow-100 text-yellow-800', icon: Pause },
      completed: { label: 'Completed', color: 'bg-gray-100 text-gray-800', icon: CheckCircle },
      cancelled: { label: 'Cancelled', color: 'bg-red-100 text-red-800', icon: XCircle },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.planning;
    const Icon = config.icon;
    
    return (
      <Badge className={config.color}>
        <Icon className="w-3 h-3 mr-1" />
        {config.label}
      </Badge>
    );
  };

  // Priority badge helper
  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      low: { label: 'Low', color: 'bg-gray-100 text-gray-800' },
      medium: { label: 'Medium', color: 'bg-blue-100 text-blue-800' },
      high: { label: 'High', color: 'bg-orange-100 text-orange-800' },
      urgent: { label: 'Urgent', color: 'bg-red-100 text-red-800' },
    };

    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.medium;
    
    return (
      <Badge variant="outline" className={config.color}>
        {config.label}
      </Badge>
    );
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Table columns
  const columns = useMemo(() => [
    {
      key: 'name',
      label: 'Project',
      render: (project: Project) => (
        <div className="space-y-1">
          <div className="font-medium">{project.name}</div>
          {project.description && (
            <div className="text-sm text-muted-foreground line-clamp-1">
              {project.description}
            </div>
          )}
          <div className="flex items-center gap-2">
            {getStatusBadge(project.status)}
            {getPriorityBadge(project.priority)}
          </div>
        </div>
      )
    },
    {
      key: 'progress',
      label: 'Progress',
      render: (project: Project) => (
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <Progress value={project.progress} className="w-16" />
            <span className="text-sm font-medium">{Math.round(project.progress)}%</span>
          </div>
        </div>
      )
    },
    {
      key: 'manager',
      label: 'Manager',
      render: (project: Project) => (
        <div className="text-sm">
          {project.manager ? (
            <div className="flex items-center gap-1">
              <Users className="w-4 h-4 text-muted-foreground" />
              {project.manager.firstName} {project.manager.lastName}
            </div>
          ) : (
            <span className="text-muted-foreground">Unassigned</span>
          )}
        </div>
      )
    },
    {
      key: 'dates',
      label: 'Timeline',
      render: (project: Project) => (
        <div className="text-sm space-y-1">
          {project.startDate && (
            <div className="flex items-center gap-1 text-muted-foreground">
              <Calendar className="w-4 h-4" />
              {formatDate(project.startDate)}
            </div>
          )}
          {project.endDate && (
            <div className="flex items-center gap-1 text-muted-foreground">
              <Calendar className="w-4 h-4" />
              Due: {formatDate(project.endDate)}
            </div>
          )}
        </div>
      )
    },
    {
      key: 'budget',
      label: 'Budget',
      render: (project: Project) => (
        <div className="text-sm">
          {project.budget ? (
            <div className="flex items-center gap-1">
              <DollarSign className="w-4 h-4 text-muted-foreground" />
              {formatCurrency(project.budget)}
            </div>
          ) : (
            <span className="text-muted-foreground">Not set</span>
          )}
        </div>
      )
    },
    {
      key: 'tasks',
      label: 'Tasks',
      render: (project: Project) => (
        <div className="text-sm text-center">
          {project._count?.tasks || 0}
        </div>
      )
    }
  ], []);

  // Table actions
  const tableActions = useMemo(() => [
    {
      label: 'View',
      icon: Eye,
      onClick: (project: Project) => router.push(`/projects/${project.id}`),
      permission: { resource: PermissionResource.PROJECTS, action: PermissionAction.READ },
      tooltip: 'View project details',
      color: 'blue' as const
    },
    {
      label: 'Edit',
      icon: Edit,
      onClick: (project: Project) => router.push(`/projects/${project.id}/edit`),
      permission: { resource: PermissionResource.PROJECTS, action: PermissionAction.UPDATE },
      tooltip: 'Edit project details',
      color: 'green' as const
    },
    {
      label: 'Delete',
      icon: Trash2,
      onClick: (project: Project) => {
        showDeleteDialog({
          ...deleteConfigurations.project(project.name),
          onConfirm: async () => {
            await handleDelete(project.id);
          }
        });
      },
      permission: { resource: PermissionResource.PROJECTS, action: PermissionAction.DELETE },
      tooltip: 'Delete this project',
      color: 'red' as const
    }
  ], [router, showDeleteDialog]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">All Projects</h1>
          <p className="text-muted-foreground">
            Manage and track all your projects
          </p>
        </div>
        <PermissionGate
          resource={PermissionResource.PROJECTS}
          action={PermissionAction.CREATE}
        >
          <Button asChild>
            <Link href="/projects/new">
              <Plus className="h-4 w-4 mr-2" />
              New Project
            </Link>
          </Button>
        </PermissionGate>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search projects..."
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={filters.status} onValueChange={(value) => handleFilterChange('status', value)}>
              <SelectTrigger>
                <SelectValue placeholder="All Statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Statuses</SelectItem>
                <SelectItem value="planning">Planning</SelectItem>
                <SelectItem value="in_progress">In Progress</SelectItem>
                <SelectItem value="on_hold">On Hold</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>

            <Select value={filters.priority} onValueChange={(value) => handleFilterChange('priority', value)}>
              <SelectTrigger>
                <SelectValue placeholder="All Priorities" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Priorities</SelectItem>
                <SelectItem value="low">Low</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="high">High</SelectItem>
                <SelectItem value="urgent">Urgent</SelectItem>
              </SelectContent>
            </Select>

            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">
                {total} project{total !== 1 ? 's' : ''} found
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Projects Table */}
      {projects.length === 0 && !loading ? (
        <Card>
          <CardContent>
            <EmptyState
              icon={<FolderOpen className="w-12 h-12" />}
              title="No projects found"
              description="Get started by creating your first project or adjust your filters."
              action={{
                label: 'Create Project',
                onClick: () => router.push('/projects/new'),
                variant: 'primary'
              }}
            />
          </CardContent>
        </Card>
      ) : (
        <Card>
          <PermissionAwareTable
            data={projects}
            columns={columns}
            actions={tableActions}
            resource={PermissionResource.PROJECTS}
            idField="id"
            ownershipField="manager.id"
            loading={loading}
            emptyMessage="No projects found"
            pagination={{
              currentPage: filters.page,
              totalPages,
              onPageChange: handlePageChange,
            }}
          />
        </Card>
      )}

      {/* Delete Dialog */}
      <DialogComponent />
    </div>
  );
}
